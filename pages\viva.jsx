import React, { useState, useRef, useEffect } from 'react';
import { MdOutlineDownloadForOffline } from "react-icons/md";
import { HiRefresh } from "react-icons/hi";
import { IoCloseCircleOutline } from "react-icons/io5";
import { FaMicrophone } from "react-icons/fa";
import { IoIosVideocam } from "react-icons/io";
import { IoSend } from "react-icons/io5";
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import PreferenceModal from './modals/PreferenceModal';
import { createAvatarSynthesizer, createWebRTCConnection } from "./Utility";
import { avatarAppConfig } from "./config";
import axios from 'axios';
import * as SpeechSDK from "microsoft-cognitiveservices-speech-sdk";
import { FaUserCircle } from "react-icons/fa";
import { PiUserFocusFill } from "react-icons/pi";
import { jsPD<PERSON> } from "jspdf";
import logoBase64 from './logoBase64';
import { delay } from 'framer-motion';

/* ────────────────────────────────────────────────────────────────────────── */
/*  🔈 1.  PUT YOUR SPEECH KEY & REGION IN ENV VARIABLES (recommended)       */
/* ------------------------------------------------------------------------ */
const SPEECH_KEY = import.meta.env.VITE_AZURE_SPEECH_KEY;      // or a hard-coded test key
const SPEECH_REGION = import.meta.env.VITE_AZURE_SPEECH_REGION; // e.g. "eastus"


const Viva = () => {
    // Chat state
    const [avatarSynthesizer, setAvatarSynthesizer] = useState(null);
    const [interviewStarted, setInterviewStarted] = useState(false);
    const [language, setLanguage] = useState("hindi");
    const [topic, setTopic] = useState("General Knowledge");
    const [input, setInput] = useState('');
    const [initialMessagesDone, setInitialMessagesDone] = useState(false);
    const [messages, setMessages] = useState([
        {
            from: 'ai',
            text: "Hey! No worries—we can turn this around. First, tell me: what topics are covered in the exam?",
        },
        {
            from: 'user',
            text: "Viva",
        },
    ]);

    useEffect(() => {
        const stored = JSON.parse(localStorage.getItem('userVivaPreference'));
        console.log("stored", stored);
        setTopic(stored.topic);
        setLanguage(stored.language);
    }, [topic, language]);

    // Webcam state
    const [webcamOn, setWebcamOn] = useState(false);
    const videoRef = useRef(null);
    const streamRef = useRef(null);
    const avatarVideoRef = useRef();
    const avatarAudioRef = useRef();

    const speakText = (text) => {
        return new Promise((resolve, reject) => {
            if (!avatarSynthesizer) return reject("Avatar not ready.");
            avatarSynthesizer.speakTextAsync(
                text,
                (result) => {
                    if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) resolve();
                    else reject("Synthesis failed.");
                },
                (error) => reject(error)
            );
        });
    };

    const handleOnTrack = (event) => {
        if (event.track.kind === "video") {
            avatarVideoRef.current.srcObject = event.streams[0];
        } else if (event.track.kind === "audio") {
            avatarAudioRef.current.srcObject = event.streams[0];
            avatarAudioRef.current.muted = false;
            avatarAudioRef.current.play().catch(() => { });
        }
    };

    const startSession = async () => {
        console.log("Starting session...");
        // const allowed = await requestMicAccess();
        // if (!allowed) return;
        // await startUserCamera();

        const pc = createWebRTCConnection(
            avatarAppConfig.iceUrl,
            avatarAppConfig.iceUsername,
            avatarAppConfig.iceCredential
        );
        pc.ontrack = handleOnTrack;
        pc.addTransceiver("video", { direction: "sendrecv" });
        pc.addTransceiver("audio", { direction: "sendrecv" });

        const avatar = createAvatarSynthesizer({
            voice: language === "hindi" ? "hi-IN-SwaraNeural" : "en-IN-NeerjaNeural",
        });
        await avatar.startAvatarAsync(pc);
        setAvatarSynthesizer(avatar);
        setInterviewStarted(true);
        // setStatus("Interview started.");
        delay
        if (!initialMessagesDone && avatarSynthesizer) {
            setInitialMessagesDone(true);
            speakText("Hello! I am your interviewer Neerja. This is an Ai Interview / viva. Shall we begin?");
        }

    };

    // Speech recognition state
    const {
        transcript,
        listening,
        resetTranscript,
        browserSupportsSpeechRecognition
    } = useSpeechRecognition();
    const [micActive, setMicActive] = useState(false);

    // Modal state
    const [showPreferenceModal, setShowPreferenceModal] = useState(true);
    const [userPreference, setUserPreference] = useState(null);
    const [initialPref, setInitialPref] = useState({ topic: '', language: '' });


    const askOpenAi = async (input) => {
        console.log("chat input input", input);
        //setMessages([...messages, { from: 'user', text: input.toString() }]);
        setMessages(prev => [...prev, { from: 'user', text: input }]);
        try {
            const response = await axios.post(
                'https://api.openai.com/v1/chat/completions',
                {
                    model: 'gpt-3.5-turbo',
                    messages: [
                        {
                            role: "system",
                            content: `Given the Topic "${topic}".
                                    You are an AI Interviewer conducting a realistic interview. Your role is to evaluate the user's knowledge and responses, only providing answers if the user explicitly asks for them.
                                    Engaging in the Interview: Start with a simple question relevant to the user's field to ease them into the interview. For example:
                                    If they are in technology: "Can you briefly explain what a computer is?"
                                    If they are in medicine: "Can you tell me what a stethoscope is used for?"
                                    If they are in finance: "What is the purpose of a bank?"
                                    Adapt the question accordingly based on their field.
                                    Guidelines:
                                    Do not provide answers unless the user directly asks.
                                    Encourage the user to elaborate on their responses.
                                    Offer feedback or hints only if the user seems uncertain.
                                    Don't ask like based on the Data you provided.
                                    Don't comment on the Topic.
                                    Ask one question at a time about "${topic}". User's last answer is: ${messages[messages.length - 1]?.text || "none"}`
                        },
                        { role: "user", content: input },
                    ],
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`
                    }
                }
            );

            console.log("response from ai", response);

            const reply = response.data.choices[0].message.content;
            console.log("reply", reply);
            //setMessages([...messages, { from: 'ai', text: reply }]);
            setMessages(prev => [...prev, { from: 'ai', text: reply }]);
            await speakText(reply);
            return reply;
        } catch (err) {
            console.log("error in fetching", err.response?.data || err.message || err);
        }
    };

    useEffect(() => {
        console.log('messages--', messages);
    }, [messages]);


    // On mount, check localStorage for userVivaPreference
    useEffect(() => {
        const stored = localStorage.getItem('userVivaPreference');
        if (stored) {
            try {
                const parsed = JSON.parse(stored);
                setInitialPref({
                    topic: parsed.topic || '',
                    language: parsed.language || ''
                });
                setUserPreference(parsed);
            } catch {
                // ignore parse error
            }
        }
        setShowPreferenceModal(true); // Always show modal on load
    }, []);

    // Webcam logic
    useEffect(() => {
        if (webcamOn) {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(stream => {
                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                        streamRef.current = stream;
                    }
                })
                .catch(() => {
                    setWebcamOn(false);
                });
        } else {
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
                streamRef.current = null;
            }
            if (videoRef.current) {
                videoRef.current.srcObject = null;
            }
        }
        return () => {
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
                streamRef.current = null;
            }
        };
    }, [webcamOn]);

    // Mic logic
    // useEffect(() => {
    //     if (micActive && browserSupportsSpeechRecognition) {
    //         resetTranscript();
    //         SpeechRecognition.startListening({ continuous: true, language: 'en-IN' });
    //     } else {
    //         SpeechRecognition.stopListening();
    //     }
    //     // When mic is stopped, update input with transcript
    //     if (!micActive && transcript) {
    //         setInput(transcript);
    //     }
    //     // eslint-disable-next-line
    // }, [micActive]);

    // Update input live while listening
    useEffect(() => {
        if (micActive && listening) {
            setInput(transcript);
        }
        // eslint-disable-next-line
    }, [transcript, listening]);

    // Send message
    const handleSend = async () => {
        if (!input.trim()) return;
        // setMessages([...messages, { from: 'user', text: input }]);
        askOpenAi(input);
        setInput('');
    };

    // Toggle webcam
    const handleToggleWebcam = () => {
        setWebcamOn(prev => !prev);
    };

    // Toggle mic
    const handleToggleMic = () => {
        if (!browserSupportsSpeechRecognition) {
            alert('Your browser does not support speech recognition.');
            return;
        }
        setMicActive(prev => !prev);
    };
    const recognizerRef = useRef(null);
    const autoSendTimeoutRef = useRef(null);

    /* ───── 3. Azure Speech Recognition logic ───────────────────────────── */
    const startAzureMic = () => {
        /* configure recognizer */
        const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
            SPEECH_KEY,
            SPEECH_REGION
        );
        speechConfig.speechRecognitionLanguage = "en-IN";

        const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
        const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

        recognizer.recognizing = (_, e) => {
            /* live stream while speaking */
            setInput(e.result.text);

            // Clear any existing timeout when user is actively speaking
            if (autoSendTimeoutRef.current) {
                clearTimeout(autoSendTimeoutRef.current);
                autoSendTimeoutRef.current = null;
            }
        };

        recognizer.recognized = (_, e) => {
            if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
                const finalText = e.result.text.trim();
                setInput(finalText);

                // Set timeout to auto-send after 1 second of silence
                if (finalText) {
                    autoSendTimeoutRef.current = setTimeout(() => {
                        // Auto-send the recognized text
                        // askOpenAi(finalText);
                        // setInput(''); // Clear input after sending
                        autoSendTimeoutRef.current = null;
                        setMicActive(false);
                    }, 2000); // delay
                }
            }
        };

        recognizer.canceled = (_, e) => {
            console.error("Recognition canceled:", e);
            recognizer.stopContinuousRecognitionAsync();
            setMicActive(false);

            // Clear timeout on cancel
            if (autoSendTimeoutRef.current) {
                clearTimeout(autoSendTimeoutRef.current);
                autoSendTimeoutRef.current = null;
            }
        };

        recognizer.sessionStopped = () => {
            recognizer.stopContinuousRecognitionAsync();
            setMicActive(false);

            // Clear timeout on session stop
            if (autoSendTimeoutRef.current) {
                clearTimeout(autoSendTimeoutRef.current);
                autoSendTimeoutRef.current = null;
            }
        };

        recognizer.startContinuousRecognitionAsync();
        recognizerRef.current = recognizer;
    };

    const stopAzureMic = () => {
        recognizerRef.current?.stopContinuousRecognitionAsync(() => {
            recognizerRef.current?.close();
            recognizerRef.current = null;
        });

        // Clear any pending auto-send timeout when stopping mic
        if (autoSendTimeoutRef.current) {
            clearTimeout(autoSendTimeoutRef.current);
            autoSendTimeoutRef.current = null;
        }
    };

    /* toggle mic */
    useEffect(() => {
        if (micActive) startAzureMic();
        else stopAzureMic();
        // cleanup on unmount
        return stopAzureMic;
    }, [micActive]);

    const downloadChatAsPDF = () => {
        const doc = new jsPDF({ unit: 'pt' }); // Use points for units

        const pageWidth = doc.internal.pageSize.getWidth(); // Width of the page in points
        const margin = 36; // Margin in points (approx 0.5 inch as 1 inch = 72 points)
        const topDocMargin = 50; // Increased top margin to accommodate logo
        let yPosition = topDocMargin; // Initial Y position for drawing
        const contentMaxWidth = pageWidth - (2 * margin); // Max width for text content within margins

        // Get current date and time in a nicely formatted string
        const now = new Date();
        const downloadDateTime = now.toLocaleDateString('en-GB') + ' ' +
            now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

        // --- Header Section ---
        // "Topic Tile Fetched" - larger and bold
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(16);
        const lessonTitle = topic || "Chat Topic";
        doc.text(`${lessonTitle}`, margin, 25);

        // Logo and AI Classroom text at top right
        try {
            // Ensure logoBase64 has proper data URL format
            let logoData = logoBase64;
            if (!logoData.startsWith('data:')) {
                // If logoBase64 doesn't have data URL prefix, add it
                logoData = `data:image/png;base64,${logoBase64}`;
            }

            const logoWidth = 80; // Changed from 45 to 80
            const logoHeight = 45; // Kept original height
            const logoX = pageWidth - margin - logoWidth - 20;
            doc.addImage(logoData, 'PNG', logoX, 15, logoWidth, logoHeight);
        } catch (error) {
            console.warn('Could not add logo to PDF:', error);
        }


        yPosition += 18 * 1.2; // Current yPosition = 50 + 21.6 = 71.6

        // Date and time of download
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
        doc.text(` ${downloadDateTime}`, margin, 45); // Fixed position for date

        // Space after the entire header block before chat content begins
        yPosition = 70; // A fixed start after the header elements

        // --- Chat Messages Section ---
        const chatFontSize = 10;
        const originalChatLineHeight = chatFontSize * 3;

        const splitTextIntoLines = (text, maxWidthForSplit) => {
            return doc.splitTextToSize(text, maxWidthForSplit);
        };

        // Fixed: Use your actual messages array and correct property names
        const messagesSource = messages;

        // Fixed: Filter messages correctly - skip first two messages (AI greeting and "Viva" response)
        const messagesToDisplay = messagesSource.filter((message, index) => {
            return index > 1 && message.text && message.text.trim() !== '';
        });

        const boxPadding = 8; //
        const textLineHeightInBox = chatFontSize * 2; //

        for (let i = 0; i < messagesToDisplay.length; i++) {
            let message = messagesToDisplay[i];
            let nextMessage = (i + 1 < messagesToDisplay.length) ? messagesToDisplay[i + 1] : null;

            doc.setFont('helvetica', 'normal'); // Reset default font for each block
            doc.setFontSize(chatFontSize);

            if (message.from === 'user' && nextMessage && nextMessage.from === 'ai') {
                const prefixUser = 'Me : ';
                // Fixed: Use 'text' property instead of 'content'
                const userContent = prefixUser + message.text;

                const userLines = splitTextIntoLines(userContent, contentMaxWidth - (2 * boxPadding));

                const prefixAI = 'AI : ';
                // Fixed: Use 'text' property instead of 'content'
                const aiContent = prefixAI + nextMessage.text;
                const aiLines = splitTextIntoLines(aiContent, contentMaxWidth - (2 * boxPadding));

                const userTextHeight = userLines.length * textLineHeightInBox;
                const aiTextHeight = aiLines.length * textLineHeightInBox;

                let spaceBetweenUserAndAIInBox = 0;
                if (userLines.length > 0 && aiLines.length > 0) {
                    spaceBetweenUserAndAIInBox = textLineHeightInBox * 0.6; // Gap between Me and AI text within the box
                }

                const contentHeightInBox = userTextHeight + spaceBetweenUserAndAIInBox + aiTextHeight;
                const boxHeight = contentHeightInBox + (3 * boxPadding); // Total height of the box

                // Page break check for the entire box
                if (yPosition + boxHeight > doc.internal.pageSize.getHeight() - topDocMargin) {
                    doc.addPage();
                    yPosition = topDocMargin;
                }

                // Draw the box
                doc.setDrawColor(200, 200, 200); // Light grey border for the box
                doc.setFillColor(248, 248, 248); // Very light grey fill for the box
                doc.rect(margin, yPosition, contentMaxWidth, boxHeight, 'FD'); // Fill and Draw

                // Render text inside the box
                // Initial baseline for the first line of text inside the box
                let yTextInBox = yPosition + boxPadding + chatFontSize;

                userLines.forEach(line => {
                    doc.text(line, margin + boxPadding, yTextInBox);
                    yTextInBox += textLineHeightInBox;
                });

                // If there was user text and there will be AI text, add the specific gap
                if (userLines.length > 0 && aiLines.length > 0) {
                    yTextInBox += spaceBetweenUserAndAIInBox;
                }

                aiLines.forEach(line => {
                    doc.text(line, margin + boxPadding, yTextInBox);
                    yTextInBox += textLineHeightInBox;
                });

                yPosition += boxHeight + (textLineHeightInBox * 0.75); // Move yPosition down by box height + some margin after the box

                i++; // Increment i because we've processed the nextMessage as well
            } else {
                // --- ORIGINAL MESSAGE RENDERING (SINGLE MESSAGE) ---
                doc.setFont('helvetica', 'normal');
                doc.setFontSize(chatFontSize);

                const prefix = message.from === 'user' ? 'Me : ' : 'AI : ';
                // Fixed: Use 'text' property instead of 'content'
                const fullTextMessage = prefix + message.text;
                const textLines = splitTextIntoLines(fullTextMessage, contentMaxWidth); // Use full contentMaxWidth

                // Original block-level page break estimation
                const originalBlockEstimatedHeight = textLines.length * originalChatLineHeight;
                const originalGapAfterAiMessage = (message.from === 'ai' && (i + 1) < messagesToDisplay.length) ? originalChatLineHeight : 0;
                const originalTotalSpaceNeededForBlock = originalBlockEstimatedHeight + originalGapAfterAiMessage;

                if (textLines.length > 0 && (yPosition + originalTotalSpaceNeededForBlock) > (doc.internal.pageSize.getHeight() - topDocMargin) && yPosition !== topDocMargin) {
                    doc.addPage();
                    yPosition = topDocMargin;
                }

                // Original line-by-line rendering with its per-line page break logic
                textLines.forEach((line) => {
                    // If the space for the current line (using originalChatLineHeight) would overflow
                    if ((yPosition + originalChatLineHeight) > (doc.internal.pageSize.getHeight() - topDocMargin) && yPosition !== topDocMargin) {
                        doc.addPage();
                        yPosition = topDocMargin;
                    }
                    doc.text(line, margin, yPosition); // yPosition is the baseline
                    yPosition += originalChatLineHeight; // Increment to next baseline using original large spacing
                });

                // Original gap logic after an AI message
                if (message.from === 'ai' && (i + 1) < messagesToDisplay.length) {
                    // The original code just added this. If it caused overflow,
                    // the *next* message's block check would handle it.
                    yPosition += originalChatLineHeight;
                }
            }
        }


        const feedbackText = "Quantum mechanics is a fascinating branch of physics that deals with the behavior of " +
            "very small particles like atoms and subatomic particles. It explores how these particles " +
            "can exist in multiple states at once and how they can exhibit both wave-like and " +
            "particle-like properties. If you have any specific questions, feel free to ask! " +
            "Quantum mechanics is a fascinating branch of physics that deals with the behavior of " +
            "very small particles";

        const feedbackLines = splitTextIntoLines(feedbackText, contentMaxWidth - (2 * boxPadding));
        const feedbackTextHeight = feedbackLines.length * textLineHeightInBox;
        const feedbackBoxHeight = feedbackTextHeight + (3 * boxPadding);

        // Check if we need a new page for the feedback box
        if (yPosition + feedbackBoxHeight > doc.internal.pageSize.getHeight() - topDocMargin) {
            doc.addPage();
            yPosition = topDocMargin;
        }


        doc.setDrawColor(220, 220, 220);
        doc.setFillColor(220, 220, 220);
        doc.rect(margin, yPosition, contentMaxWidth, feedbackBoxHeight, 'FD');


        doc.setFont('helvetica', 'bold');
        doc.setFontSize(12);
        doc.setTextColor(50, 50, 50); // White text
        doc.text("Overall Feedback", margin + boxPadding, yPosition + boxPadding + 12);


        doc.setFont('helvetica', 'normal');
        doc.setFontSize(chatFontSize);
        let yFeedbackText = yPosition + boxPadding + 12 + textLineHeightInBox;

        feedbackLines.forEach(line => {
            doc.text(line, margin + boxPadding, yFeedbackText);
            yFeedbackText += textLineHeightInBox;
        });

        doc.save(`chat_export_${now.toISOString().split('T')[0]}.pdf`);
    };

    return (
        <>
            {showPreferenceModal && (
                <PreferenceModal
                    onClose={() => setShowPreferenceModal(false)}
                    onSubmit={(pref) => {
                        setUserPreference(pref);
                        setInitialPref(pref);
                        setShowPreferenceModal(false);
                        localStorage.setItem("userVivaPreference", JSON.stringify(pref));
                        startSession();
                    }}
                    initialTopic={initialPref.topic}
                    initialLanguage={initialPref.language}
                />
            )}
            {!showPreferenceModal && (
                <div className="min-h-screen bg-white flex flex-col items-center justify-center py-8 px-2 md:px-0">
                    {/* Header */}
                    <div className="w-full max-w-5xl flex justify-between items-center mb-8">
                        <div className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 font-bold text-3xl md:text-4xl" style={{ WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
                            AI Interview - LIVE
                        </div>
                        <div className="flex gap-4 text-[#3734FF]">
                            <button onClick={downloadChatAsPDF}>
                                <MdOutlineDownloadForOffline className="w-7 h-7 rounded-full flex items-center justify-center text-lg hover:text-blue-600 cursor-pointer" />
                            </button>
                            <HiRefresh className="w-7 h-7 rounded-fullflex items-center justify-center text-lg" />
                            <IoCloseCircleOutline className="w-7 h-7 rounded-full flex items-center justify-center text-lg" />
                        </div>
                    </div>

                    <div className="w-full max-w-7xl border-1 border-gray-200 rounded-2xl p-6 flex flex-col md:flex-row gap-8 bg-[#D9D9D91A]">
                        {/* AI Cam Section */}
                        <div className="flex-1 flex flex-col items-center">
                            <div className="relative w-[70%] h-[100%] rounded-2xl overflow-hidden border border-gray-200 shadow-lg bg-white flex items-center justify-center mb-0">
                                {/* AI Cam Image Placeholder */}
                                <div className="absolute top-4 right-4 z-10">
                                    {/* Placeholder for waveform icon */}
                                    <span className="w-10 h-6 bg-blue-200 rounded-full flex items-center justify-center text-blue-700 font-bold">〰️</span>
                                </div>
                                {/* <img src="https://www.indianext.co.in/wp-content/uploads/2022/03/portrait-female-teacher-holding-notepad-green.jpg" alt="AI Cam" className="object-cover w-full h-full" /> */}
                                <video
                                    ref={avatarVideoRef}
                                    className="w-full h-full object-cover bg-white"
                                    autoPlay
                                    muted
                                />
                                <audio ref={avatarAudioRef} />
                                {/* AI message overlay */}
                                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 w-[90%] bg-gradient-to-r from-[#6C9DFF] to-[#755CFF] rounded-lg px-4 py-2 text-white text-sm shadow-lg">
                                    {messages.filter(m => m.from === 'ai').slice(-1)[0]?.text}
                                </div>
                            </div>
                        </div>

                        {/* User Cam & Chat Section */}
                        <div className="flex-1 flex flex-col items-center">
                            <div className="w-[90%] h-[400px] rounded-2xl overflow-hidden border border-gray-200 shadow-lg bg-gray-100 flex items-center justify-center mb-0">
                                {webcamOn ? (
                                    <video ref={videoRef} autoPlay muted className="object-cover w-full h-full" />
                                ) : (
                                    <div className="w-full h-full flex flex-col items-center justify-center">
                                        {/* <FaUserCircle className="w-1/3 h-1/3 text-gray-400" /> */}
                                        <PiUserFocusFill className="w-1/3 h-1/3 text-gray-400" />
                                        <div className="w-full flex items-center justify-center text-gray-600 text-2xl">Camera Turned Off</div>
                                    </div>
                                )}
                            </div>
                            {/* Mic and Camera buttons below video */}
                            <div className="flex gap-6 mt-4 mb-4 justify-center">
                                <button
                                    className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl shadow-md transition ${micActive ? 'bg-gradient-to-r from-[#A723FF] to-[#1A39FF]' : 'bg-red-600'}`}
                                    onClick={handleToggleMic}
                                >
                                    <FaMicrophone className="w-6 h-6" />
                                </button>
                                <button
                                    className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl shadow-md transition ${webcamOn ? 'bg-gradient-to-r from-[#A723FF] to-[#1A39FF]' : 'bg-red-600'}`}
                                    onClick={handleToggleWebcam}
                                >
                                    <IoIosVideocam className="w-6 h-6" />
                                </button>
                            </div>
                            {/* Chat input below buttons */}
                            <div className="w-full flex items-center gap-2 mt-2">
                                <input
                                    className="flex-1 px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-200 focus:border-transparent text-base"
                                    type="text"
                                    placeholder="Your Text Here....."
                                    value={input}
                                    onChange={e => setInput(e.target.value)}
                                    onKeyDown={e => { if (e.key === 'Enter') handleSend(); }}
                                    disabled={micActive}
                                />
                                <button
                                    className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-200 to-blue-200 flex items-center justify-center text-gray-700 hover:text-purple-700 transition text-xl"
                                    onClick={handleSend}
                                    disabled={micActive}
                                >
                                    <IoSend className="w-6 h-6" />
                                </button>
                            </div>

                        </div>

                    </div>
                </div>
            )}
        </>
    );
};

export default Viva;