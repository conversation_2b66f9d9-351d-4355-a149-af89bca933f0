import { useState, useCallback, useEffect } from 'react';
import { format, isValid, parse, addDays, differenceInCalendarDays } from 'date-fns';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';

const SetSchedule = () => {
    const [userId, setUserId] = useState(null);
    const navigate = useNavigate();
    const [startDate, setStartDate] = useState(new Date());
    const [endDate, setEndDate] = useState(new Date());
    const [daysCount, setDaysCount] = useState(12);
    const [hoursCount, setHoursCount] = useState(2);
    const [startTime, setStartTime] = useState(dayjs().set('hour', 9).set('minute', 0));
    const [selectedDays, setSelectedDays] = useState({
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false,
        sunday: false
    });
    const [selectedSecondDays, setSelectedSecondDays] = useState({
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false,
        sunday: false
    });
    const [showStartDatePicker, setShowStartDatePicker] = useState(false);
    const [showEndDatePicker, setShowEndDatePicker] = useState(false);
    const [csvFile, setCsvFile] = useState(null);

    // Day picker handler functions
    const handleStartDateSelect = (day) => {
        setStartDate(day);
        setEndDate(addDays(day, daysCount - 1));
        setShowStartDatePicker(false);
    };

    const handleEndDateSelect = (day) => {
        setEndDate(day);
        setDaysCount(differenceInCalendarDays(day, startDate) + 1);
        setShowEndDatePicker(false);
    };

    const toggleStartDatePicker = () => {
        setShowStartDatePicker(!showStartDatePicker);
        if (showEndDatePicker) setShowEndDatePicker(false);
    };


    const toggleEndDatePicker = () => {
        setShowEndDatePicker(!showEndDatePicker);
        if (showStartDatePicker) setShowStartDatePicker(false);
    };

    const incrementDays = () => {
        setDaysCount(prev => {
            const newCount = Math.min(prev + 1, 30);
            setEndDate(addDays(startDate, newCount - 1));
            return newCount;
        });
    };

    const decrementDays = () => {
        setDaysCount(prev => {
            const newCount = Math.max(prev - 1, 1);
            setEndDate(addDays(startDate, newCount - 1));
            return newCount;
        });
    };

    const incrementHours = () => {
        setHoursCount(prev => Math.min(prev + 1, 12));
    };

    const decrementHours = () => {
        setHoursCount(prev => Math.max(prev - 1, 1));
    };

    const toggleDay = (day, setFunction) => {
        setFunction(prev => ({
            ...prev,
            [day]: !prev[day]
        }));
    };

    const selectAllDays = (setFunction) => {
        setFunction({
            monday: true,
            tuesday: true,
            wednesday: true,
            thursday: true,
            friday: true,
            saturday: true,
            sunday: true
        });
    };

    const handleFileUpload = (e) => {
        const file = e.target.files[0];
        if (file) {
            setCsvFile(file);
        }
    };

    const handleTimeChange = (newTime) => {
        setStartTime(newTime);
    };

    useEffect(() => {
        const storedUserData = localStorage.getItem("user-data");
        if (storedUserData) {
            try {
                const parsedUserData = JSON.parse(storedUserData);
                setUserId(parsedUserData.userId);
            } catch (err) {
                console.error("Error parsing user data from localStorage", err);
            }
        }
    }, [userId]);

    useEffect(() => {
        const data = JSON.parse(localStorage.getItem("airf"));
        console.log(data);
        console.log("searchPrompt from local storage", data.searchPrompt);
        console.log("specifications from local storage", data.specifications);
        console.log("language from local storage", data.language);
        console.log("uploadedFileName from local storage", data.uploadedFileName);
        console.log("manualEntryText from local storage", data.manualEntryText);

    }, []);

    const handleSkip = () => {
        const Airfdata = JSON.parse(localStorage.getItem("airf"));

        const dataForBreakdown = {
            "wbStrId": null,
            "shouldDoPostReq": true,
        }
        localStorage.setItem("dataForBreakdown", JSON.stringify(dataForBreakdown));

        const dataToSend = {
            "userId": userId,
            "planner": {
                "promptTopic": Airfdata.searchPrompt ? Airfdata.searchPrompt : Airfdata.specifications,
                "language": Airfdata.language === "English" ? "en" : "hi",
                "manualEntryText": Airfdata.manualEntryText ? Airfdata.manualEntryText : Airfdata.uploadedFileName,
                "pdfLink": Airfdata.pdfUrl
            }
        }
        localStorage.removeItem('videoSectionChatMessages');
        localStorage.setItem("dataToSend", JSON.stringify(dataToSend));
        navigate("/structured-breakdown");
    }

    const handleProceed = async () => {

        const dataForBreakdown = {
            "wbStrId": null,
            "shouldDoPostReq": true,
        }
        localStorage.setItem("dataForBreakdown", JSON.stringify(dataForBreakdown));
        localStorage.removeItem('videoSectionChatMessages');

        // Structure data for API call
        const selectedDaysArray = Object.entries(selectedDays)
            .filter(([_, isSelected]) => isSelected)
            .map(([day]) => day);

        const selectedSecondDaysArray = Object.entries(selectedSecondDays)
            .filter(([_, isSelected]) => isSelected)
            .map(([day]) => day);

        const scheduleData = {
            startDate: format(startDate, 'yyyy-MM-dd'),
            endDate: format(endDate, 'yyyy-MM-dd'),
            startTime: startTime ? startTime.format('hh:mm A') : '09:00 AM',
            daysCount,
            hoursCount,
            selectedDays: selectedDaysArray,
            selectedSecondDays: selectedSecondDaysArray
        };

        // console.log('Data to be sent to API:', scheduleData);

        const Airfdata = JSON.parse(localStorage.getItem("airf"));

        const dataToSend = {
            "userId": userId,
            "planner": {
                "promptTopic": Airfdata.searchPrompt ? Airfdata.searchPrompt : Airfdata.specifications,
                "startDate": format(startDate, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
                "noOfDays": daysCount,
                "language": Airfdata.language === "English" ? "en" : "hi",
                "selectedDays": selectedDaysArray,
                "manualEntryText": Airfdata.manualEntryText ? Airfdata.manualEntryText : Airfdata.uploadedFileName,
                "pdfLink": Airfdata.pdfUrl
                // "selectedSecondDays": selectedSecondDaysArray
            }
        }

        localStorage.setItem("dataToSend", JSON.stringify(dataToSend));

        navigate("/structured-breakdown");

        const postData = async () => {
            const url = 'http://localhost:8000/api/v1/planner/createStudyPlan';
            const data = JSON.stringify({
                "userId": "6833a66fec30e890d74ae595",
                "planner": {
                    "promptTopic": "Hello",
                    "startDate": format(startDate, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
                    "noOfDays": daysCount,
                    "language": "en",
                    "selectedDays": selectedDaysArray,
                    // "selectedSecondDays": selectedSecondDaysArray
                }
            })

            console.log('Data to be sent to API:', data);

            const headers = {
                'Content-Type': 'application/json',
            };

            try {
                const response = await axios.post(url, data, { headers });
                console.log('POST success:', response.data);
            } catch (error) {
                console.error('POST error:', error);
            }
        };

        // await postData();

    };

    const translator = (word1, word2) =>
        localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
            ? word1
            : localStorage.getItem("lang")
                ? word2
                : word1;

    return (
        <div className="w-full min-h-screen bg-[#F9FAFC] flex items-center justify-center py-6">
            <div className="w-[1200px] bg-white rounded-2xl shadow-lg overflow-hidden border pb-[100px] border-gray-100 relative">
                {/* Close button */}
                <button className="absolute right-4 top-4 text-white opacity-80 hover:opacity-100">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>

                {/* Header with gradient background */}
                <div className="bg-gradient-to-r from-[#A78BFA] to-[#818CF8] p-6 text-white">
                    <h1 className="text-2xl font-semibold">{translator("Set Your Study Timeline", "अपना अध्ययन टाइमलाइन सेट करें")}</h1>
                    <p className="text-sm opacity-90 mt-1">{translator("Design your schedule that suits your needs", "अपने जरूरतों के अनुसार अपना अध्ययन टाइमलाइन डिजाइन करें")}</p>
                </div>
                <div className="">
                    {/* Main content */}
                    <div className="p-6">
                        <h2 className="text-lg font-medium text-[#1E1E2F] mb-4">{translator("Set your Schedule Timeline", "अपना अध्ययन टाइमलाइन सेट करें")}</h2>

                        {/* Date selection with inline "To" */}
                        <div className="flex items-center space-x-4 mb-6 justify-between">
                            <div className="w-[230px] relative">
                                <label className="block text-xs font-medium text-gray-600 mb-1">{translator("Start", "शुरू")}</label>
                                <div
                                    className="relative cursor-pointer w-[203px]"
                                    onClick={() => {
                                        setShowStartDatePicker(true);
                                    }}
                                >
                                    <div className="w-full py-2 px-3 border border-gray-300 rounded-lg text-base focus:outline-none focus:ring-1 focus:ring-indigo-500 flex justify-between items-center">
                                        <span>{format(startDate, 'yyyy-MM-dd')}</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                            <line x1="16" y1="2" x2="16" y2="6"></line>
                                            <line x1="8" y1="2" x2="8" y2="6"></line>
                                            <line x1="3" y1="10" x2="21" y2="10"></line>
                                        </svg>
                                    </div>
                                    {showStartDatePicker && (
                                        <div className="absolute z-10 w-[300px] bg-white shadow-lg rounded-lg border border-gray-200 mt-1">
                                            <DayPicker
                                                mode="single"
                                                selected={startDate}
                                                onSelect={handleStartDateSelect}
                                                className="p-2"
                                                disabled={[{ before: new Date() }]}
                                            />


                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="flex items-center justify-center mt-6 w-[50%]">
                                <div className="flex-grow h-px bg-gray-300"></div>
                                {/* <span className="text-gray-400 mx-4">{translator("OR", "या")}</span> */}
                                <div className="flex-grow h-px bg-gray-300"></div>
                            </div>

                            <div>
                                <label className="block text-xs font-medium text-gray-600 mb-1">{translator("Choose No of Days", "कितने दिन चुनें")}</label>
                                <div className="flex rounded-lg overflow-hidden border border-gray-300">
                                    <button
                                        onClick={decrementDays}
                                        className="w-10 h-9 bg-gray-100 flex items-center justify-center text-lg font-medium hover:bg-gray-200"
                                    >
                                        -
                                    </button>
                                    <div className="w-10 h-9 border-l border-r border-gray-300 flex items-center justify-center text-base font-medium">
                                        {daysCount}
                                    </div>
                                    <button
                                        onClick={incrementDays}
                                        className="w-10 h-9 bg-gray-100 flex items-center justify-center text-lg font-medium hover:bg-gray-200"
                                    >
                                        +
                                    </button>
                                </div>
                            </div>
                            <div className="flex items-center justify-center mt-6 w-[50%]">
                                <div className="w-[100%] h-[2px] bg-gray-300"></div>
                                {/* <span className="text-gray-400 mx-4">{translator("To", "तक")}</span> */}
                                <div className="w-[100%] h-[2px] bg-gray-300"></div>
                            </div>

                            <div className="w-[230px] relative">
                                <label className="block text-xs font-medium text-gray-600 mb-1 ">{translator("Finish", "समाप्त")}</label>
                                <div
                                    className="relative cursor-pointer w-[203px]"
                                    onClick={() => {
                                        setShowEndDatePicker(true);
                                    }}
                                >
                                    <div className="w-full py-2 px-3 border border-gray-300 rounded-lg text-base focus:outline-none focus:ring-1 focus:ring-indigo-500 flex justify-between items-center">
                                        <span>{format(endDate, 'yyyy-MM-dd')}</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                            <line x1="16" y1="2" x2="16" y2="6"></line>
                                            <line x1="8" y1="2" x2="8" y2="6"></line>
                                            <line x1="3" y1="10" x2="21" y2="10"></line>
                                        </svg>
                                    </div>
                                    {showEndDatePicker && (
                                        <div className="absolute z-10 bg-white shadow-lg rounded-lg border border-gray-200 mt-1 right-0">
                                            <DayPicker
                                                mode="single"
                                                selected={endDate}
                                                onSelect={handleEndDateSelect}
                                                className="p-2"
                                                disabled={[{ before: startDate }]}
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>


                        </div>

                        {/* First Row: Hours + Select Days */}
                        <div className="flex gap-6 mt-2">
                            <div className="w-1/2">
                                <h3 className="text-lg font-medium text-[#1E1E2F] mb-3">{translator("At What Time You Prefer to Start ?", "आप किस समय पर शुरू करना चाहते हैं?")}</h3>

                                <div>
                                    <label className="block text-xs font-medium text-gray-600 mb-1">{translator("Choose Start Time", "शुरू करने का समय चुनें")}</label>
                                    <div className="w-[250px]">
                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                            <TimePicker
                                                value={startTime}
                                                onChange={handleTimeChange}
                                                format="hh:mm a"
                                                ampm={true}
                                                className="w-full"
                                                slotProps={{
                                                    textField: {
                                                        variant: 'outlined',
                                                        size: 'small',
                                                        className: 'w-full',
                                                    }
                                                }}
                                            />
                                        </LocalizationProvider>
                                    </div>
                                </div>
                            </div>

                            <div className="w-1/2">
                                <div className="flex items-center mb-3">
                                    <h3 className="text-base font-medium">{translator("Select Days", "दिन चुनें")}</h3>
                                    <button
                                        onClick={() => selectAllDays(setSelectedDays)}
                                        className="text-xs text-gray-500 hover:underline ml-2 hover:text-indigo-600 transition-colors duration-300"
                                    >
                                        {translator("Select ALL", "सभी चुनें")}
                                    </button>
                                </div>

                                <div className="flex flex-wrap gap-3">
                                    {Object.keys(selectedDays).map((day) => (
                                        <div
                                            key={day}
                                            className="flex items-center space-x-2 cursor-pointer"
                                            onClick={() => toggleDay(day, setSelectedDays)}
                                        >
                                            <input
                                                type="checkbox"
                                                id={`day-${day}`}
                                                checked={selectedDays[day]}
                                                onChange={() => { }}
                                                className="w-4 h-4 rounded border-2 accent-indigo-600 transition-transform duration-200 hover:scale-110"
                                            />
                                            <label htmlFor={`day-${day}`} className="capitalize text-sm cursor-pointer">
                                                {day}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Horizontal divider with OR */}
                        {/* <div className="flex items-center my-6">
                        <div className="flex-grow h-[2px] bg-[#7560FF]"></div>
                        <span className="px-4 text-sm text-gray-500">{translator("OR", "या")}</span>
                        <div className="flex-grow h-[2px] bg-[#7560FF]"></div>
                    </div> */}

                        {/* Second Row: CSV Upload + Select Days */}
                        {/* <div className="flex gap-6">
                        <div className="w-1/2">
                            <div>
                                <div className="flex items-center justify-between">
                                    <h3 className="text-base font-medium">{translator("Upload CSV File", "CSV फाइल अपलोड करें")}</h3>
                                    <label htmlFor="csv-upload" className="text-blue-600 transition-all duration-300 hover:scale-110 hover:text-indigo-700 cursor-pointer">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="7 10 12 15 17 10"></polyline>
                                            <line x1="12" y1="15" x2="12" y2="3"></line>
                                        </svg>
                                    </label>
                                    <input
                                        type="file"
                                        id="csv-upload"
                                        accept=".csv"
                                        className="hidden"
                                        onChange={handleFileUpload}
                                    />
                                </div>
                                <button className="text-gray-500 text-xs hover:underline hover:text-indigo-600 transition-colors duration-300">Download Format</button>

                                {csvFile ? (
                                    <div className="mt-2 bg-gray-50 rounded-lg p-3 flex items-center">
                                        <div className="w-6 h-6 text-red-600 mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                <polyline points="14 2 14 8 20 8"></polyline>
                                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                                <polyline points="10 9 9 9 8 9"></polyline>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 className="text-sm font-medium">{csvFile.name}</h4>
                                            <span className="text-gray-500 text-xs">{(csvFile.size / (1024 * 1024)).toFixed(2)} MB</span>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="mt-2 bg-gray-50 rounded-lg p-3 flex items-center border border-dashed border-gray-300">
                                        <p className="text-gray-500 text-sm text-center w-full">{translator("No file selected", "कोई फाइल चुनी नहीं गई")}</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="w-1/2">
                            <h3 className="text-lg font-medium text-[#1E1E2F] mb-3">{translator("At What Time You Prefer to Start ?", "आप किस समय पर शुरू करना चाहते हैं?")}</h3>

                            <div>
                                <label className="block text-xs font-medium text-gray-600 mb-1">{translator("Choose Start Time", "शुरू करने का समय चुनें")}</label>
                                <div className="w-[250px]">
                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <TimePicker
                                            value={startTime}
                                            onChange={handleTimeChange}
                                            format="hh:mm a"
                                            ampm={true}
                                            className="w-full"
                                            slotProps={{
                                                textField: {
                                                    variant: 'outlined',
                                                    size: 'small',
                                                    className: 'w-full',
                                                }
                                            }}
                                        />
                                    </LocalizationProvider>
                                </div>
                            </div>
                        </div>
                    </div> */}

                        {/* Action buttons */}
                        <div className="flex justify-between mt-6 pt-4">

                            <button className="py-3 px-6 rounded-lg text-base font-medium transition-all duration-300">
                            </button>

                            <button
                                onClick={handleProceed}
                                className="py-3 px-6 bg-gradient-to-r from-[#6BA0FF] to-[#755BFF] text-white rounded-lg text-base font-semibold relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] before:absolute before:content-[''] before:top-0 before:left-0 before:w-full before:h-full before:bg-white/20 before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-500"
                            >
                                {translator("Proceed With Study Plan", "अध्ययन योजना के साथ जारी रखें")}
                            </button>

                            <button
                                onClick={handleSkip}
                                className="py-3 px-6 bg-white border border-gray-300 rounded-lg text-base font-medium transition-all duration-300 hover:bg-gray-50 hover:shadow-md hover:scale-[1.02] active:scale-[0.98]">
                                {translator("Skip", "छोड़ें")}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SetSchedule;

